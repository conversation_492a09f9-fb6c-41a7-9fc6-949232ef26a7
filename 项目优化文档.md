# 项目优化文档

## 1. 项目现状分析

### 1.1 项目基本信息
- **项目名称**: @micro-core/monorepo
- **版本**: 0.1.0
- **技术栈**: TypeScript 5.3+, Vite 7.0.4, Vitest 3.2.4, pnpm 8.15.0
- **架构模式**: Monorepo + 微内核架构
- **包管理**: pnpm workspace

### 1.2 当前目录结构分析

#### 核心业务目录
- `packages/core/` - 微前端核心内核 ✅
- `packages/shared/` - 共享工具和类型 ✅
- `packages/adapters/` - 框架适配器集合 ✅
- `packages/plugins/` - 插件系统 ✅
- `packages/builders/` - 构建工具适配器 ✅
- `packages/sidecar/` - Sidecar模式支持 ✅
- `apps/` - 示例应用和测试应用 ✅
- `docs/` - 技术文档 ✅

#### 测试目录现状
- `tests/` - 集成测试、E2E测试、性能测试 ✅
- `test/` - 旧版测试目录 ⚠️ (需要合并)
- 各包内的 `__tests__/` 目录 ✅

#### 配置和工具文件
- 根目录配置文件完整 ✅
- 脚本目录组织良好 ✅
- Docker配置完整 ✅

### 1.3 识别的问题

#### 文档管理问题
- 根目录存在大量重构相关的临时文档
- 中文文档与英文文档混合存放
- 技术文档分散在多个位置

#### 测试结构问题
- 存在 `test/` 和 `tests/` 两个测试目录
- 测试配置文件分散
- 部分包的测试结构不统一

#### 文件分类问题
- 根目录存在多个重构报告文件
- 临时文件和正式文档混合
- 备份文件已存在但需要进一步整理

## 2. 优化方案总览

### 2.1 优化目标
1. **标准化项目结构** - 建立清晰的目录层次和命名规范
2. **统一测试架构** - 整合测试目录，建立统一的测试标准
3. **规范文档管理** - 集中管理技术文档，分离临时文件
4. **优化包结构** - 进一步细化shared包的职责划分
5. **完善工具链** - 统一构建、测试、发布流程

### 2.2 优化原则
- **最小化破坏性变更** - 保持现有功能完整性
- **渐进式重构** - 分阶段实施，确保项目稳定性
- **标准化优先** - 遵循现代软件工程最佳实践
- **可维护性提升** - 提高代码质量和开发效率

## 3. 详细迁移计划

### 3.1 文档整理和迁移 (优先级: 高)

#### 3.1.1 临时文档迁移到备份目录
**目标**: 清理根目录，保持项目结构清洁

| 原始路径 | 目标路径 | 优化理由 | 影响评估 |
|---------|---------|---------|---------|
| `./PACKAGES_REFACTOR_COMPLETE_REPORT.md` | `_backup/refactor-reports/PACKAGES_REFACTOR_COMPLETE_REPORT.md` | 重构完成报告，属于历史文档 | 无影响，纯文档迁移 |
| `./PACKAGES_REFACTOR_PROGRESS.md` | `_backup/refactor-reports/PACKAGES_REFACTOR_PROGRESS.md` | 重构进度报告，属于历史文档 | 无影响，纯文档迁移 |
| `./REFACTOR_COMPLETION_REPORT.md` | `_backup/refactor-reports/REFACTOR_COMPLETION_REPORT.md` | 重构完成报告，属于历史文档 | 无影响，纯文档迁移 |
| `./REFACTOR_REPORT.md` | `_backup/refactor-reports/REFACTOR_REPORT.md` | 重构报告，属于历史文档 | 无影响，纯文档迁移 |
| `./packages-optimization-final-report.md` | `_backup/refactor-reports/packages-optimization-final-report.md` | 优化报告，属于历史文档 | 无影响，纯文档迁移 |
| `./packages-optimization-progress.md` | `_backup/refactor-reports/packages-optimization-progress.md` | 优化进度，属于历史文档 | 无影响，纯文档迁移 |
| `./packages目录深度分析与重构清单.md` | `_backup/refactor-reports/packages目录深度分析与重构清单.md` | 分析清单，属于历史文档 | 无影响，纯文档迁移 |
| `./packages重构实施手册.md` | `_backup/refactor-reports/packages重构实施手册.md` | 实施手册，属于历史文档 | 无影响，纯文档迁移 |
| `./全面梳理清单.md` | `_backup/refactor-reports/全面梳理清单.md` | 梳理清单，属于历史文档 | 无影响，纯文档迁移 |
| `./完整目录结构设计.md` | `_backup/refactor-reports/完整目录结构设计.md` | 设计文档，属于历史文档 | 无影响，纯文档迁移 |
| `./开发设计指导方案.md` | `_backup/refactor-reports/开发设计指导方案.md` | 指导方案，属于历史文档 | 无影响，纯文档迁移 |

#### 3.1.2 重构指南文档处理
| 原始路径 | 目标路径 | 优化理由 | 影响评估 |
|---------|---------|---------|---------|
| `./README-重构指南.md` | `docs/zh/guide/refactor-guide.md` | 重构指南应归入技术文档 | 需要更新文档索引 |

#### 3.1.3 测试配置文件整理
| 原始路径 | 目标路径 | 优化理由 | 影响评估 |
|---------|---------|---------|---------|
| `./test-coverage-improved.cjs` | `_backup/legacy-configs/test-coverage-improved.cjs` | 旧版测试覆盖率配置 | 无影响，已被新配置替代 |
| `./test-coverage.cjs` | `_backup/legacy-configs/test-coverage.cjs` | 旧版测试覆盖率配置 | 无影响，已被新配置替代 |
| `./test-coverage.js` | `_backup/legacy-configs/test-coverage.js` | 旧版测试覆盖率配置 | 无影响，已被新配置替代 |
| `./jest.config.js` | `_backup/legacy-configs/jest.config.js` | 旧版Jest配置，已迁移到Vitest | 无影响，已被Vitest替代 |
| `./jest.setup.js` | `_backup/legacy-configs/jest.setup.js` | 旧版Jest设置，已迁移到Vitest | 无影响，已被Vitest替代 |

### 3.2 测试结构统一化 (优先级: 高)

#### 3.2.1 测试目录合并
| 原始路径 | 目标路径 | 优化理由 | 影响评估 |
|---------|---------|---------|---------|
| `./test/setup.ts` | `./tests/setup/legacy-setup.ts` | 合并到统一的测试设置目录 | 需要更新测试配置引用 |
| `./test/shared/` | `./tests/shared/` | 合并共享测试工具 | 需要更新导入路径 |

#### 3.2.2 测试配置标准化
- 统一使用 `vitest.config.ts` 作为测试配置
- 所有测试文件采用 `*.test.ts` 或 `*.spec.ts` 命名
- 测试目录结构与源码目录结构保持一致

### 3.3 包结构进一步优化 (优先级: 中)

#### 3.3.1 packages/shared 细化重组
当前shared包已经有良好的基础结构，建议进一步细化：

```
packages/shared/
├── constants/          # 常量定义
├── types/             # 类型定义
├── utils/             # 工具函数
├── helpers/           # 辅助函数
├── errors/            # 错误处理 (新增)
├── logging/           # 日志记录 (新增)
├── validation/        # 数据验证 (新增)
└── testing/           # 测试工具 (重组)
```

#### 3.3.2 新增模块规划
基于当前项目分析，建议新增以下模块：

1. **packages/shared/errors/** - 统一错误处理
   - 标准化错误类型定义
   - 错误码管理
   - 错误格式化工具

2. **packages/shared/logging/** - 日志系统
   - 统一日志接口
   - 日志级别管理
   - 日志格式化

3. **packages/shared/validation/** - 数据验证
   - 配置验证器
   - 类型检查工具
   - 数据格式验证

## 4. 新增模块规划

### 4.1 错误处理模块 (packages/shared/errors/)
```typescript
// packages/shared/errors/src/index.ts
export * from './error-codes';
export * from './error-types';
export * from './error-formatter';
export * from './error-handler';
```

**职责范围**:
- 定义标准化错误类型和错误码
- 提供错误格式化和处理工具
- 统一错误报告和日志记录接口

### 4.2 日志记录模块 (packages/shared/logging/)
```typescript
// packages/shared/logging/src/index.ts
export * from './logger';
export * from './log-levels';
export * from './log-formatter';
export * from './log-transport';
```

**职责范围**:
- 提供统一的日志记录接口
- 支持多种日志级别和格式
- 可配置的日志传输和存储

### 4.3 数据验证模块 (packages/shared/validation/)
```typescript
// packages/shared/validation/src/index.ts
export * from './validators';
export * from './schemas';
export * from './type-guards';
export * from './config-validator';
```

**职责范围**:
- 提供通用数据验证工具
- 配置文件验证器
- 运行时类型检查工具

## 5. 标准化规范

### 5.1 目录命名规范
- **包目录**: 使用 kebab-case 命名 (如 `adapter-react`)
- **源码目录**: 使用 kebab-case 命名 (如 `error-handler`)
- **测试目录**: 统一使用 `__tests__` 或 `tests`
- **配置目录**: 使用描述性名称 (如 `configs`, `scripts`)

### 5.2 文件命名规范
- **TypeScript文件**: 使用 kebab-case + `.ts` (如 `error-handler.ts`)
- **测试文件**: 使用 `*.test.ts` 或 `*.spec.ts`
- **配置文件**: 使用描述性名称 (如 `vitest.config.ts`)
- **文档文件**: 使用 kebab-case + `.md` (如 `api-reference.md`)

### 5.3 包结构规范
每个包必须包含以下标准文件：
- `README.md` - 包说明文档
- `package.json` - 包配置文件
- `tsconfig.json` - TypeScript配置
- `tsup.config.ts` - 构建配置
- `vitest.config.ts` - 测试配置

### 5.4 代码组织规范
- **单一职责**: 每个模块职责明确，避免功能重叠
- **依赖管理**: 最小化包间依赖，避免循环依赖
- **接口优先**: 定义清晰的公共接口，隐藏实现细节
- **类型安全**: 严格的TypeScript类型定义

## 6. 实施建议

### 6.1 实施阶段划分

#### 阶段一：文档整理 (优先级: 高, 预计1-2天)
**目标**: 清理根目录，规范文档管理

**具体任务**:
1. 创建 `_backup/refactor-reports/` 目录
2. 迁移所有重构相关文档到备份目录
3. 创建 `_backup/legacy-configs/` 目录
4. 迁移旧版配置文件到备份目录
5. 将 `README-重构指南.md` 迁移到 `docs/zh/guide/refactor-guide.md`
6. 更新文档索引和导航

**验收标准**:
- 根目录只保留核心项目文件
- 所有临时文档已分类存档
- 文档导航更新完成

#### 阶段二：测试结构统一 (优先级: 高, 预计2-3天)
**目标**: 建立统一的测试架构

**具体任务**:
1. 合并 `test/` 目录内容到 `tests/`
2. 统一测试文件命名规范
3. 更新所有测试配置引用
4. 验证测试套件完整性
5. 更新CI/CD配置

**验收标准**:
- 只存在一个 `tests/` 目录
- 所有测试正常运行
- 测试覆盖率保持不变

#### 阶段三：包结构优化 (优先级: 中, 预计3-5天)
**目标**: 完善shared包的模块化结构

**具体任务**:
1. 创建新的shared子模块目录
2. 设计和实现错误处理模块
3. 设计和实现日志记录模块
4. 设计和实现数据验证模块
5. 重构现有代码使用新模块
6. 更新包依赖关系

**验收标准**:
- 新模块功能完整且测试覆盖
- 现有功能无破坏性变更
- 包依赖关系清晰

#### 阶段四：验证和测试 (优先级: 高, 预计1-2天)
**目标**: 确保所有变更的正确性

**具体任务**:
1. 运行完整测试套件
2. 验证构建流程
3. 检查文档完整性
4. 性能回归测试
5. 更新相关文档

**验收标准**:
- 所有测试通过
- 构建成功
- 文档完整准确
- 性能无回退

### 6.2 风险控制措施

#### 6.2.1 备份策略
- **完整备份**: 在开始重构前创建完整项目备份
- **分阶段备份**: 每个阶段完成后创建检查点
- **回滚机制**: 准备快速回滚脚本和流程

#### 6.2.2 测试策略
- **持续测试**: 每次变更后立即运行相关测试
- **回归测试**: 定期运行完整测试套件
- **性能监控**: 监控关键性能指标

#### 6.2.3 协作策略
- **分支开发**: 使用功能分支进行开发
- **代码审查**: 所有变更必须经过代码审查
- **文档同步**: 及时更新相关文档

### 6.3 成功标准

#### 6.3.1 技术指标
- ✅ 所有单元测试通过 (覆盖率 100%)
- ✅ 所有集成测试通过
- ✅ 构建流程正常 (构建时间 ≤ 5分钟)
- ✅ 包大小控制 (核心包 ≤ 12KB)
- ✅ 无TypeScript类型错误
- ✅ 无ESLint警告

#### 6.3.2 结构指标
- ✅ 目录结构清晰规范
- ✅ 文档组织有序
- ✅ 包职责划分明确
- ✅ 依赖关系简洁
- ✅ 命名规范统一

#### 6.3.3 可维护性指标
- ✅ 代码复用率提升
- ✅ 新功能开发效率提升
- ✅ 问题定位时间缩短
- ✅ 文档查找便捷
- ✅ 开发体验改善

### 6.4 后续维护建议

#### 6.4.1 定期审查
- **月度审查**: 检查目录结构和文档完整性
- **季度审查**: 评估包结构和依赖关系
- **年度审查**: 全面评估架构合理性

#### 6.4.2 持续改进
- **收集反馈**: 定期收集开发团队反馈
- **优化流程**: 持续优化开发和构建流程
- **更新标准**: 根据最佳实践更新规范

#### 6.4.3 知识管理
- **文档维护**: 保持文档的及时更新
- **经验总结**: 定期总结优化经验
- **培训分享**: 向团队分享最佳实践

---

## 总结

本优化文档基于对micro-core项目的深入分析，制定了全面的结构优化方案。通过分阶段实施，可以在保持项目稳定性的前提下，显著提升项目的可维护性和开发效率。

**关键优势**:
- 📁 **结构清晰**: 标准化的目录结构和命名规范
- 🧪 **测试统一**: 统一的测试架构和配置
- 📚 **文档规范**: 集中化的文档管理
- 🔧 **工具完善**: 完整的开发工具链
- 🚀 **易于维护**: 模块化的包结构设计

**注意事项**:
- 本文档仅为优化规划，不执行任何实际的文件操作
- 所有建议基于项目实际情况制定，具备可操作性
- 建议在实施前进行充分的测试和验证
- 保持与团队的充分沟通，确保变更的一致性

通过执行本优化方案，micro-core项目将具备更加现代化、标准化的项目结构，为后续的功能开发和维护奠定坚实基础。

---

# 文件级别详细优化扩展

## 1. packages/core 文件级别分析与优化

### 1.1 当前文件结构问题分析

#### 1.1.1 packages/core/src/index.ts - 主入口文件
**当前问题**:
- 文件过于庞大（150+行），违反单一职责原则
- 包含了MicroCore主类定义，应该独立成文件
- 导出语句与类定义混合，影响可读性

**重构方案**:
```typescript
// 新的文件结构
packages/core/src/
├── index.ts              # 纯导出文件，不包含实现
├── micro-core.ts         # MicroCore主类定义
├── exports/              # 分类导出
│   ├── runtime.ts        # 运行时组件导出
│   ├── communication.ts  # 通信模块导出
│   ├── sandbox.ts        # 沙箱模块导出
│   └── types.ts          # 类型导出
```

**优化后的 index.ts**:
```typescript
/**
 * @fileoverview Micro-Core 主入口文件
 * @description 统一导出所有公共API，不包含具体实现
 */

// 主类导出
export { MicroCore } from './micro-core';

// 分模块导出
export * from './exports/runtime';
export * from './exports/communication';
export * from './exports/sandbox';
export * from './exports/types';

// 常量和工具
export * from './constants';
export * from './errors';
export * from './utils';
```

#### 1.1.2 packages/core/src/types.ts - 类型定义文件
**当前问题**:
- 仅作为重新导出文件，但文件名容易与types/目录混淆
- 向后兼容导出过多，增加维护负担

**重构方案**:
```typescript
// 重命名为 legacy-types.ts，明确标识为兼容性文件
packages/core/src/legacy-types.ts

// 添加废弃警告
/**
 * @deprecated 此文件仅为向后兼容保留
 * 请直接从 './types/index' 导入类型定义
 */
```

#### 1.1.3 packages/core/src/utils.ts - 工具函数文件
**当前问题**:
- 仅作为shared包的重新导出，但保留在core包中容易造成混淆
- 开发环境警告机制不够明显

**重构方案**:
```typescript
// 重命名为 legacy-utils.ts
packages/core/src/legacy-utils.ts

// 增强废弃警告
if (process.env.NODE_ENV === 'development') {
    console.warn(
        '⚠️  [DEPRECATED] 从 @micro-core/core 导入工具函数已废弃\n' +
        '   请改为从 @micro-core/shared/utils 导入\n' +
        '   此兼容性导出将在下个主版本中移除'
    );
}
```

### 1.2 runtime/ 目录文件优化

#### 1.2.1 packages/core/src/runtime/kernel.ts - 内核文件
**当前问题**:
- 文件过大（400+行），承担过多职责
- 包含路由监听、应用管理、插件管理等多个职责
- 延迟初始化的组件管理不够清晰

**重构方案**:
```typescript
// 拆分为多个文件
packages/core/src/runtime/
├── kernel/
│   ├── index.ts              # 内核主类
│   ├── kernel-core.ts        # 核心功能
│   ├── kernel-lifecycle.ts   # 生命周期管理
│   ├── kernel-plugins.ts     # 插件管理
│   └── kernel-routing.ts     # 路由管理
```

**优化后的 kernel/index.ts**:
```typescript
/**
 * @fileoverview 微前端内核主类
 * @description 内核的统一入口，协调各个子系统
 */

import { KernelCore } from './kernel-core';
import { KernelLifecycle } from './kernel-lifecycle';
import { KernelPlugins } from './kernel-plugins';
import { KernelRouting } from './kernel-routing';

export class MicroCoreKernel {
    private core: KernelCore;
    private lifecycle: KernelLifecycle;
    private plugins: KernelPlugins;
    private routing: KernelRouting;

    constructor(options: MicroCoreOptions = {}) {
        this.core = new KernelCore(options);
        this.lifecycle = new KernelLifecycle(this.core);
        this.plugins = new KernelPlugins(this.core);
        this.routing = new KernelRouting(this.core);
    }

    // 委托方法到相应的子系统
    registerApplication = this.core.registerApplication.bind(this.core);
    start = this.lifecycle.start.bind(this.lifecycle);
    stop = this.lifecycle.stop.bind(this.lifecycle);
    // ... 其他方法
}
```

#### 1.2.2 packages/core/src/runtime/app-loader.ts - 应用加载器
**当前问题**:
- 包含多种入口类型处理逻辑，职责不够单一
- 缺少加载策略的抽象

**重构方案**:
```typescript
packages/core/src/runtime/loader/
├── index.ts                  # AppLoader主类
├── strategies/               # 加载策略
│   ├── html-loader.ts        # HTML入口加载
│   ├── js-loader.ts          # JS入口加载
│   └── multi-loader.ts       # 多入口加载
├── cache/                    # 缓存管理
│   ├── loader-cache.ts       # 加载缓存
│   └── cache-strategy.ts     # 缓存策略
└── parsers/                  # 资源解析
    ├── lifecycle-parser.ts   # 生命周期解析
    └── resource-parser.ts    # 资源解析
```

### 1.3 types/ 目录结构优化

#### 当前问题分析:
- 类型文件过于分散，部分类型定义重复
- 缺少类型的层次结构和依赖关系管理

**重构方案**:
```typescript
packages/core/src/types/
├── index.ts                  # 主导出文件
├── core/                     # 核心类型
│   ├── kernel.ts             # 内核相关类型
│   ├── application.ts        # 应用相关类型
│   └── configuration.ts      # 配置相关类型
├── runtime/                  # 运行时类型
│   ├── lifecycle.ts          # 生命周期类型
│   ├── loader.ts             # 加载器类型
│   └── registry.ts           # 注册中心类型
├── communication/            # 通信类型
│   ├── events.ts             # 事件类型
│   └── messages.ts           # 消息类型
├── sandbox/                  # 沙箱类型
│   ├── base.ts               # 基础沙箱类型
│   └── strategies.ts         # 沙箱策略类型
└── utils/                    # 工具类型
    ├── common.ts             # 通用工具类型
    └── helpers.ts            # 辅助类型
```

## 2. packages/shared 文件重构设计

### 2.1 当前结构问题

#### 2.1.1 功能重叠问题
- `utils/` 和 `helpers/` 目录功能边界不清晰
- `constants/` 中部分常量与 `types/` 中的枚举重复定义
- 多个子包之间存在循环依赖风险

#### 2.1.2 文件组织问题
- `utils/src/index.ts` 文件过大，导出过多
- 缺少明确的模块边界和职责划分

### 2.2 重构设计方案

#### 2.2.1 新的目录结构
```typescript
packages/shared/
├── core/                     # 核心基础设施
│   ├── constants/            # 核心常量
│   ├── types/                # 核心类型
│   └── errors/               # 错误处理
├── utils/                    # 纯工具函数
│   ├── type-check/           # 类型检查
│   ├── object/               # 对象操作
│   ├── array/                # 数组操作
│   ├── string/               # 字符串处理
│   ├── function/             # 函数工具
│   └── async/                # 异步工具
├── helpers/                  # 业务逻辑辅助
│   ├── validation/           # 数据验证
│   ├── formatting/           # 格式化工具
│   ├── configuration/        # 配置管理
│   └── performance/          # 性能监控
├── adapters/                 # 适配器通用工具
│   ├── common/               # 通用适配器工具
│   └── strategies/           # 适配器策略
└── testing/                  # 测试工具
    ├── mocks/                # 模拟工具
    ├── fixtures/             # 测试数据
    └── helpers/              # 测试辅助
```

#### 2.2.2 模块职责重新划分

**utils/ - 纯工具函数**
- 无副作用的纯函数
- 不依赖外部状态
- 可独立测试和使用

**helpers/ - 业务逻辑辅助**
- 包含业务逻辑的辅助函数
- 可能依赖配置或状态
- 为特定场景设计

**adapters/ - 适配器工具**
- 专门为适配器系统设计
- 包含通用的适配器模式实现
- 支持策略模式扩展

### 2.3 具体文件重构示例

#### 2.3.1 utils/src/index.ts 重构
**当前问题**: 文件过大，导出混乱

**重构方案**:
```typescript
// 新的 utils/src/index.ts
/**
 * @fileoverview 工具函数统一导出
 * @description 按功能分类导出，避免命名冲突
 */

// 类型检查工具
export * as TypeCheck from './type-check';

// 对象操作工具
export * as ObjectUtils from './object';

// 数组操作工具
export * as ArrayUtils from './array';

// 字符串处理工具
export * as StringUtils from './string';

// 函数工具
export * as FunctionUtils from './function';

// 异步工具
export * as AsyncUtils from './async';

// 向后兼容的平铺导出（标记为废弃）
export * from './legacy-exports';
```

#### 2.3.2 新增错误处理模块
```typescript
packages/shared/core/errors/
├── index.ts                  # 错误处理主导出
├── error-codes.ts            # 错误码定义
├── error-types.ts            # 错误类型定义
├── error-factory.ts          # 错误工厂
├── error-handler.ts          # 错误处理器
└── error-recovery.ts         # 错误恢复策略
```

## 3. 文件级别标准化规范

### 3.1 文件命名规范

#### 3.1.1 基础规范
- **源码文件**: 使用 kebab-case，如 `app-loader.ts`
- **类型文件**: 使用 kebab-case + `.types.ts` 后缀，如 `app-loader.types.ts`
- **测试文件**: 使用 `.test.ts` 或 `.spec.ts` 后缀
- **配置文件**: 使用描述性名称，如 `vitest.config.ts`

#### 3.1.2 特殊文件命名
- **入口文件**: 统一使用 `index.ts`
- **常量文件**: 使用 `constants.ts` 或 `const.ts`
- **工具文件**: 使用 `utils.ts` 或具体功能名
- **兼容性文件**: 使用 `legacy-` 前缀

### 3.2 文件内部组织标准

#### 3.2.1 导入顺序规范
```typescript
// 1. Node.js 内置模块
import { readFileSync } from 'fs';
import { resolve } from 'path';

// 2. 第三方库
import { debounce } from 'lodash';
import axios from 'axios';

// 3. 内部模块 - 按层级排序
import { MicroCoreError } from '../errors';
import { createLogger } from '../utils';
import type { AppConfig } from '../types';

// 4. 相对导入
import { validateConfig } from './validation';
import type { LocalConfig } from './types';
```

#### 3.2.2 文件结构模板
```typescript
/**
 * @fileoverview 文件功能描述
 * @description 详细的功能说明
 * <AUTHOR>
 * @version 版本号
 * @since 添加版本
 */

// === 导入部分 ===
// 按照导入顺序规范组织

// === 类型定义 ===
export interface LocalInterface {
    // 接口定义
}

export type LocalType = string | number;

// === 常量定义 ===
const LOCAL_CONSTANT = 'value';

// === 私有函数 ===
function privateHelper(): void {
    // 私有函数实现
}

// === 公共函数/类 ===
export function publicFunction(): void {
    // 公共函数实现
}

export class PublicClass {
    // 类实现
}

// === 默认导出 ===
export default PublicClass;
```

### 3.3 文件头注释标准

#### 3.3.1 标准注释模板
```typescript
/**
 * @fileoverview 文件功能的简短描述
 * @description 文件功能的详细描述，包括主要用途和设计思路
 * <AUTHOR> <邮箱地址>
 * @version 当前版本号
 * @since 首次添加的版本号
 * @lastModified 最后修改时间
 * @performance 性能相关说明（如有）
 * @security 安全相关说明（如有）
 * @example
 * ```typescript
 * // 使用示例
 * import { ExampleClass } from './example';
 * const instance = new ExampleClass();
 * ```
 */
```

#### 3.3.2 函数注释标准
```typescript
/**
 * 函数功能描述
 * @description 详细的功能说明
 * @param {Type} paramName - 参数描述
 * @param {Type} [optionalParam] - 可选参数描述
 * @returns {Type} 返回值描述
 * @throws {ErrorType} 可能抛出的错误
 * @example
 * ```typescript
 * const result = functionName(param1, param2);
 * ```
 * @since 版本号
 * @deprecated 废弃说明（如果适用）
 */
```

### 3.4 文件大小和复杂度控制

#### 3.4.1 文件大小限制
- **源码文件**: 不超过 300 行
- **类型文件**: 不超过 200 行
- **测试文件**: 不超过 500 行
- **配置文件**: 不超过 100 行

#### 3.4.2 复杂度控制
- **单个函数**: 不超过 50 行
- **单个类**: 不超过 200 行
- **嵌套层级**: 不超过 4 层
- **圈复杂度**: 不超过 10

### 3.5 依赖管理规范

#### 3.5.1 依赖层级规范
```
Level 1: shared/core (基础设施)
Level 2: shared/utils, shared/types
Level 3: shared/helpers, shared/adapters
Level 4: core, plugins, adapters
Level 5: applications
```

#### 3.5.2 循环依赖检测
- 使用工具自动检测循环依赖
- 建立依赖关系图
- 定期审查依赖结构

## 4. 测试文件结构优化

### 4.1 当前测试结构问题

#### 4.1.1 测试目录分散
- 根目录存在 `test/` 和 `tests/` 两个目录
- 各包内测试文件命名不统一
- 测试配置分散在多个文件中

#### 4.1.2 测试覆盖率不均
- 部分核心模块缺少测试
- 测试用例与源码文件对应关系不清晰

### 4.2 统一测试结构设计

#### 4.2.1 标准测试目录结构
```
packages/[package-name]/
├── src/                      # 源码目录
│   ├── module-a.ts
│   └── module-b.ts
├── __tests__/                # 测试目录
│   ├── unit/                 # 单元测试
│   │   ├── module-a.test.ts
│   │   └── module-b.test.ts
│   ├── integration/          # 集成测试
│   │   └── module-integration.test.ts
│   ├── fixtures/             # 测试数据
│   │   └── test-data.json
│   └── helpers/              # 测试辅助
│       └── test-utils.ts
└── vitest.config.ts          # 测试配置
```

#### 4.2.2 测试文件命名规范
- **单元测试**: `[模块名].test.ts`
- **集成测试**: `[功能名].integration.test.ts`
- **E2E测试**: `[场景名].e2e.test.ts`
- **性能测试**: `[模块名].perf.test.ts`

### 4.3 测试文件内容标准

#### 4.3.1 测试文件结构模板
```typescript
/**
 * @fileoverview [模块名] 单元测试
 * @description 测试 [模块名] 的所有公共API和边界情况
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ModuleUnderTest } from '../src/module-under-test';

describe('ModuleUnderTest', () => {
    let instance: ModuleUnderTest;

    beforeEach(() => {
        // 测试前置设置
        instance = new ModuleUnderTest();
    });

    afterEach(() => {
        // 测试后置清理
        vi.clearAllMocks();
    });

    describe('constructor', () => {
        it('should create instance with default options', () => {
            expect(instance).toBeInstanceOf(ModuleUnderTest);
        });

        it('should handle invalid options', () => {
            expect(() => new ModuleUnderTest(null as any)).toThrow();
        });
    });

    describe('publicMethod', () => {
        it('should return expected result for valid input', () => {
            const result = instance.publicMethod('valid-input');
            expect(result).toBe('expected-output');
        });

        it('should handle edge cases', () => {
            expect(() => instance.publicMethod('')).toThrow();
            expect(instance.publicMethod(null as any)).toBeNull();
        });
    });
});
```

## 5. 适配器系统标准化重构

### 5.1 当前适配器结构分析

#### 5.1.1 结构一致性问题
- 各适配器包结构不完全一致
- 通用功能重复实现
- 缺少统一的适配器接口

#### 5.1.2 代码复用问题
- 错误处理逻辑重复
- 配置验证逻辑重复
- 生命周期管理逻辑重复

### 5.2 标准化适配器结构

#### 5.2.1 统一目录结构
```
packages/adapters/adapter-[framework]/
├── src/
│   ├── index.ts              # 主导出文件
│   ├── adapter.ts            # 适配器主类
│   ├── lifecycle/            # 生命周期管理
│   │   ├── bootstrap.ts      # 启动逻辑
│   │   ├── mount.ts          # 挂载逻辑
│   │   └── unmount.ts        # 卸载逻辑
│   ├── utils/                # 适配器工具
│   │   ├── config.ts         # 配置处理
│   │   ├── dom.ts            # DOM操作
│   │   └── error.ts          # 错误处理
│   └── types/                # 类型定义
│       ├── config.ts         # 配置类型
│       └── lifecycle.ts      # 生命周期类型
├── __tests__/                # 测试文件
├── examples/                 # 使用示例
└── README.md                 # 文档
```

#### 5.2.2 适配器基类设计
```typescript
// packages/shared/adapters/base-adapter.ts
export abstract class BaseAdapter<TConfig = any, TInstance = any> {
    protected config: TConfig;
    protected instance: TInstance | null = null;

    constructor(config: TConfig) {
        this.config = this.validateConfig(config);
    }

    abstract validateConfig(config: TConfig): TConfig;
    abstract bootstrap(props?: any): Promise<void>;
    abstract mount(container: Element, props?: any): Promise<void>;
    abstract unmount(props?: any): Promise<void>;
    abstract update?(props?: any): Promise<void>;

    protected handleError(error: Error, context: string): never {
        throw new AdapterError(
            `${this.constructor.name}:${context}`,
            error.message,
            { originalError: error, config: this.config }
        );
    }
}
```

### 5.3 适配器工具函数标准化

#### 5.3.1 通用工具函数
```typescript
// packages/shared/adapters/common/index.ts
export interface AdapterUtils {
    // 配置管理
    validateConfig<T>(config: T, schema: any): T;
    mergeConfigs<T>(base: T, override: Partial<T>): T;

    // DOM操作
    createContainer(selector: string): Element;
    cleanupContainer(container: Element): void;

    // 错误处理
    formatAdapterError(error: Error, context: string): AdapterError;

    // 生命周期
    executeLifecycle<T>(fn: Function, context: string): Promise<T>;
}
```

## 6. 插件系统模块优化

### 6.1 当前插件结构问题

#### 6.1.1 插件接口不统一
- 不同插件的接口定义不一致
- 缺少插件生命周期的标准化管理
- 插件间依赖关系管理复杂

#### 6.1.2 插件加载机制问题
- 缺少插件的动态加载能力
- 插件错误处理不够完善
- 插件配置验证不统一

### 6.2 插件系统重构设计

#### 6.2.1 标准插件结构
```
packages/plugins/plugin-[name]/
├── src/
│   ├── index.ts              # 插件主入口
│   ├── plugin.ts             # 插件实现
│   ├── config/               # 配置管理
│   │   ├── schema.ts         # 配置模式
│   │   └── defaults.ts       # 默认配置
│   ├── hooks/                # 钩子实现
│   │   ├── install.ts        # 安装钩子
│   │   ├── activate.ts       # 激活钩子
│   │   └── deactivate.ts     # 停用钩子
│   └── utils/                # 插件工具
│       └── helpers.ts        # 辅助函数
├── __tests__/                # 测试文件
├── docs/                     # 插件文档
└── examples/                 # 使用示例
```

#### 6.2.2 插件基类设计
```typescript
// packages/shared/plugins/base-plugin.ts
export abstract class BasePlugin<TConfig = any> {
    public readonly name: string;
    public readonly version: string;
    protected config: TConfig;
    protected isInstalled = false;
    protected isActive = false;

    constructor(name: string, version: string, config?: TConfig) {
        this.name = name;
        this.version = version;
        this.config = this.validateConfig(config || {} as TConfig);
    }

    abstract validateConfig(config: TConfig): TConfig;
    abstract install(kernel: any): Promise<void>;
    abstract activate(): Promise<void>;
    abstract deactivate(): Promise<void>;
    abstract uninstall(): Promise<void>;

    // 生命周期状态管理
    public getStatus(): PluginStatus {
        if (!this.isInstalled) return 'NOT_INSTALLED';
        if (!this.isActive) return 'INSTALLED';
        return 'ACTIVE';
    }
}
```

## 7. 实施优先级和依赖关系

### 7.1 实施优先级矩阵

| 优化项目 | 优先级 | 复杂度 | 影响范围 | 预计工时 |
|---------|--------|--------|----------|----------|
| packages/core 文件拆分 | 高 | 中 | 核心模块 | 3-5天 |
| packages/shared 重构 | 高 | 高 | 全项目 | 5-7天 |
| 测试结构统一 | 高 | 低 | 测试系统 | 2-3天 |
| 适配器标准化 | 中 | 中 | 适配器系统 | 4-6天 |
| 插件系统优化 | 中 | 高 | 插件系统 | 5-8天 |
| 文件命名规范 | 低 | 低 | 全项目 | 1-2天 |

### 7.2 依赖关系图

```mermaid
graph TD
    A[文件命名规范] --> B[packages/shared 重构]
    B --> C[packages/core 文件拆分]
    B --> D[适配器标准化]
    B --> E[插件系统优化]
    C --> F[测试结构统一]
    D --> F
    E --> F
    F --> G[最终验证和测试]
```

### 7.3 分阶段实施计划

#### 阶段一：基础设施重构 (第1-2周)
1. 制定并实施文件命名规范
2. 重构 packages/shared 包结构
3. 建立新的类型系统和工具函数体系

#### 阶段二：核心模块优化 (第3-4周)
1. 拆分 packages/core 大文件
2. 重构内核和运行时组件
3. 优化类型定义结构

#### 阶段三：系统模块标准化 (第5-6周)
1. 标准化适配器系统
2. 优化插件系统架构
3. 统一测试结构

#### 阶段四：验证和完善 (第7周)
1. 运行完整测试套件
2. 性能回归测试
3. 文档更新和完善

### 7.4 质量保证措施

#### 7.4.1 代码质量检查
- ESLint 规则更新
- TypeScript 严格模式检查
- 代码覆盖率要求 ≥ 90%

#### 7.4.2 自动化工具
- 文件大小监控
- 循环依赖检测
- 性能基准测试

#### 7.4.3 人工审查
- 代码审查清单
- 架构设计评审
- 文档完整性检查

---

## 总结

本扩展文档提供了文件级别的详细优化建议，涵盖了：

1. **精确的问题识别** - 针对每个具体文件的问题分析
2. **具体的重构方案** - 提供可执行的代码结构调整
3. **标准化规范** - 建立统一的文件组织和编码标准
4. **实施路线图** - 明确的优先级和依赖关系

通过执行这些文件级别的优化，micro-core项目将实现：
- 📁 **更清晰的文件结构** - 单一职责，边界明确
- 🔧 **更好的代码复用** - 减少重复，提高效率
- 📝 **更统一的规范** - 标准化的命名和组织方式
- 🧪 **更完善的测试** - 全面的测试覆盖和结构
- 🚀 **更高的可维护性** - 易于理解、修改和扩展

所有建议都基于项目实际情况，确保可操作性和实用性。